<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="Eslint" enabled="true" level="WARNING" enabled_by_default="true" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="57">
            <item index="0" class="java.lang.String" itemvalue="GitPython" />
            <item index="1" class="java.lang.String" itemvalue="seaborn" />
            <item index="2" class="java.lang.String" itemvalue="matplotlib" />
            <item index="3" class="java.lang.String" itemvalue="pycocotools" />
            <item index="4" class="java.lang.String" itemvalue="ultralytics" />
            <item index="5" class="java.lang.String" itemvalue="numpy" />
            <item index="6" class="java.lang.String" itemvalue="gitpython" />
            <item index="7" class="java.lang.String" itemvalue="requests" />
            <item index="8" class="java.lang.String" itemvalue="scipy" />
            <item index="9" class="java.lang.String" itemvalue="darkdetect" />
            <item index="10" class="java.lang.String" itemvalue="customtkinter" />
            <item index="11" class="java.lang.String" itemvalue="sqlite3" />
            <item index="12" class="java.lang.String" itemvalue="django_import_export" />
            <item index="13" class="java.lang.String" itemvalue="tensorflow" />
            <item index="14" class="java.lang.String" itemvalue="django_simpleui" />
            <item index="15" class="java.lang.String" itemvalue="pymysql" />
            <item index="16" class="java.lang.String" itemvalue="opencv-python" />
            <item index="17" class="java.lang.String" itemvalue="Django" />
            <item index="18" class="java.lang.String" itemvalue="keras" />
            <item index="19" class="java.lang.String" itemvalue="thop" />
            <item index="20" class="java.lang.String" itemvalue="python-snmp" />
            <item index="21" class="java.lang.String" itemvalue="pandas" />
            <item index="22" class="java.lang.String" itemvalue="tqdm" />
            <item index="23" class="java.lang.String" itemvalue="gradio" />
            <item index="24" class="java.lang.String" itemvalue="joblib" />
            <item index="25" class="java.lang.String" itemvalue="scikit_learn" />
            <item index="26" class="java.lang.String" itemvalue="torch" />
            <item index="27" class="java.lang.String" itemvalue="loguru" />
            <item index="28" class="java.lang.String" itemvalue="scikit-image" />
            <item index="29" class="java.lang.String" itemvalue="email-validator" />
            <item index="30" class="java.lang.String" itemvalue="PyJWT" />
            <item index="31" class="java.lang.String" itemvalue="Werkzeug" />
            <item index="32" class="java.lang.String" itemvalue="python-opencv" />
            <item index="33" class="java.lang.String" itemvalue="scikit-learn" />
            <item index="34" class="java.lang.String" itemvalue="Flask-WTF" />
            <item index="35" class="java.lang.String" itemvalue="python-dotenv" />
            <item index="36" class="java.lang.String" itemvalue="plotly" />
            <item index="37" class="java.lang.String" itemvalue="Flask-SQLAlchemy" />
            <item index="38" class="java.lang.String" itemvalue="Flask-Login" />
            <item index="39" class="java.lang.String" itemvalue="Flask-Moment" />
            <item index="40" class="java.lang.String" itemvalue="Flask-Bootstrap4" />
            <item index="41" class="java.lang.String" itemvalue="Flask-Migrate" />
            <item index="42" class="java.lang.String" itemvalue="Flask-Mail" />
            <item index="43" class="java.lang.String" itemvalue="PyMySQL" />
            <item index="44" class="java.lang.String" itemvalue="dash" />
            <item index="45" class="java.lang.String" itemvalue="Flask" />
            <item index="46" class="java.lang.String" itemvalue="Pillow" />
            <item index="47" class="java.lang.String" itemvalue="PyQt5" />
            <item index="48" class="java.lang.String" itemvalue="jieba" />
            <item index="49" class="java.lang.String" itemvalue="selenium" />
            <item index="50" class="java.lang.String" itemvalue="webdriver-manager" />
            <item index="51" class="java.lang.String" itemvalue="wordcloud" />
            <item index="52" class="java.lang.String" itemvalue="pyecharts" />
            <item index="53" class="java.lang.String" itemvalue="flask" />
            <item index="54" class="java.lang.String" itemvalue="mysql-connector-python" />
            <item index="55" class="java.lang.String" itemvalue="PyQt6" />
            <item index="56" class="java.lang.String" itemvalue="yolov5" />
          </list>
        </value>
      </option>
    </inspection_tool>
    <inspection_tool class="PyStubPackagesAdvertiser" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <list>
          <option value="PyQt5-stubs==5.15.6.0" />
        </list>
      </option>
    </inspection_tool>
  </profile>
</component>