from flask import Flask, redirect, url_for
from flask_migrate import Migrate
from config import Config
from extensions import db
import os

def create_app():
    app = Flask(__name__)
    app.config.from_object(Config)

    # 设置编码
    app.config['JSON_AS_ASCII'] = False

    # 初始化扩展
    db.init_app(app)

    # 注册蓝图
    from routes.main import main_bp
    from routes.auth import auth_bp

    app.register_blueprint(main_bp)
    app.register_blueprint(auth_bp, url_prefix='/auth')

    # 重定向根路径到登录页面
    @app.route('/')
    def root():
        return redirect(url_for('auth.login'))

    return app

app = create_app()
migrate = Migrate(app, db)

if __name__ == '__main__':
    with app.app_context():
        # 导入所有模型以确保它们被注册
        from models import ScenicSpot, TravelPlan, User

        # 创建所有表
        db.create_all()

    app.run(host='0.0.0.0', port=5001, debug=True)