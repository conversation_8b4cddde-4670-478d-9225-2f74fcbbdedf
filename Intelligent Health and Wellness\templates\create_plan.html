{% extends "base.html" %}

{% block title %}创建旅游计划 - 智能康养旅游线路规划系统{% endblock %}

{% block extra_css %}
<style>
    #map {
        height: 400px;
        width: 100%;
    }
</style>
{% endblock %}

{% block content %}
<div class="container mt-4">
    <div class="row justify-content-center">
        <div class="col-md-8">
            <div class="card">
                <div class="card-header">
                    <h4 class="mb-0">创建新的旅游计划</h4>
                </div>
                <div class="card-body">
                    <form method="POST" action="{{ url_for('main.create_plan') }}">
                        <div class="mb-3">
                            <label for="departure" class="form-label">出发地</label>
                            <input type="text" class="form-control" id="departure" name="departure" value="西安" required>
                            <div class="form-text">请输入您的出发城市</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="destination" class="form-label">目的地</label>
                            <input type="text" class="form-control" id="destination" name="destination" required>
                            <div class="form-text">请输入您想去的目的地</div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="start_date" class="form-label">出发日期</label>
                            <input type="date" class="form-control" id="start_date" name="start_date" required>
                        </div>
                        
                        <div class="mb-3">
                            <label for="duration" class="form-label">行程天数</label>
                            <select class="form-select" id="duration" name="duration" required>
                                <option value="3">3天</option>
                                <option value="5">5天</option>
                                <option value="7">7天</option>
                                <option value="10">10天</option>
                            </select>
                        </div>
                        
                        <div class="mb-3">
                            <label class="form-label">旅行偏好</label>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="preferences" value="nature" id="nature">
                                <label class="form-check-label" for="nature">自然风光</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="preferences" value="culture" id="culture">
                                <label class="form-check-label" for="culture">文化古迹</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="preferences" value="wellness" id="wellness">
                                <label class="form-check-label" for="wellness">康养休闲</label>
                            </div>
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" name="preferences" value="food" id="food">
                                <label class="form-check-label" for="food">美食体验</label>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="special_needs" class="form-label">特殊需求</label>
                            <textarea class="form-control" id="special_needs" name="special_needs" rows="3" placeholder="如果您有任何特殊需求，请在此说明"></textarea>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-plus-circle"></i> 创建计划
                            </button>
                            <a href="{{ url_for('main.my_plans') }}" class="btn btn-secondary">
                                <i class="bi bi-arrow-left"></i> 返回列表
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // 地图初始化将由main.js中的initMap函数处理
    document.addEventListener('DOMContentLoaded', function() {
        initMap();
    });
    
    document.addEventListener('DOMContentLoaded', function() {
        // 日期验证：设置最小日期为今天
        var today = new Date();
        var dd = String(today.getDate()).padStart(2, '0');
        var mm = String(today.getMonth() + 1).padStart(2, '0');
        var yyyy = today.getFullYear();
        today = yyyy + '-' + mm + '-' + dd;
        
        var startDateInput = document.getElementById('start_date');
        startDateInput.min = today;
        
        // 表单验证
        document.getElementById('planForm').addEventListener('submit', function(e) {
            var destination = document.getElementById('destination').value.trim();
            var startDate = document.getElementById('start_date').value;
            var duration = document.getElementById('duration').value;
            var preferences = document.querySelectorAll('input[name="preferences"]:checked');
            
            if (!destination) {
                e.preventDefault();
                alert('请输入目的地');
                return false;
            }
            
            if (!startDate) {
                e.preventDefault();
                alert('请选择出发日期');
                return false;
            }
            
            if (!duration) {
                e.preventDefault();
                alert('请选择行程天数');
                return false;
            }
            
            if (preferences.length === 0) {
                e.preventDefault();
                alert('请至少选择一个旅游偏好');
                return false;
            }
            
            return true;
        });
    });
</script>
{% endblock %} 