/* 全局样式 */
body {
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

.footer {
    margin-top: auto;
    background-color: #f8f9fa;
    padding: 1rem 0;
}

/* 导航栏样式 */
.navbar {
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

.navbar-brand {
    font-weight: bold;
}

/* 卡片样式 */
.card {
    box-shadow: 0 2px 4px rgba(0,0,0,.05);
    border: none;
    margin-bottom: 1.5rem;
}

.card-title {
    color: #2c3e50;
    font-weight: 600;
}

/* 表单样式 */
.form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
}

/* 按钮样式 */
.btn-primary {
    background-color: #007bff;
    border-color: #007bff;
}

.btn-primary:hover {
    background-color: #0069d9;
    border-color: #0062cc;
}

/* 地图容器样式 */
#map {
    border-radius: 4px;
    box-shadow: 0 2px 4px rgba(0,0,0,.1);
}

/* 列表样式 */
.list-group-item {
    border: none;
    padding: 0.75rem 1.25rem;
}

.list-group-item:first-child {
    border-top-left-radius: 0;
    border-top-right-radius: 0;
}

.list-group-item:last-child {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .container {
        padding: 0 15px;
    }
    
    .card {
        margin-bottom: 1rem;
    }
    
    #map {
        height: 300px;
    }
}

/* 动画效果 */
.card {
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: translateY(-5px);
}

/* 自定义滚动条 */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: #888;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #555;
} 