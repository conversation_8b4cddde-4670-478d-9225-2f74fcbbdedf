from extensions import db
from datetime import datetime

class ScenicSpot(db.Model):
    __tablename__ = 'scenic_spots'
    
    id = db.Column(db.Integer, primary_key=True)
    name = db.Column(db.String(100), nullable=False)
    description = db.Column(db.Text)
    latitude = db.Column(db.Float, nullable=False)
    longitude = db.Column(db.Float, nullable=False)
    health_benefits = db.Column(db.Text)
    visit_duration = db.Column(db.Integer)  # 建议游览时间（分钟）
    rating = db.Column(db.Float, default=5.0)  # 景点评分
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    
    def to_dict(self):
        return {
            'id': self.id,
            'name': self.name,
            'description': self.description,
            'latitude': float(self.latitude),  # 确保是浮点数
            'longitude': float(self.longitude),  # 确保是浮点数
            'health_benefits': self.health_benefits,
            'visit_duration': self.visit_duration,
            'rating': float(self.rating) if self.rating else 5.0,  # 确保是浮点数
            'created_at': self.created_at.strftime('%Y-%m-%d %H:%M:%S') if self.created_at else None
        } 