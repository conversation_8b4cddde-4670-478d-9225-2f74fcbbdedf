{% extends "base.html" %}

{% block title %}查看旅游计划 - 智能康养旅游线路规划系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-4">
                    <h2 class="card-title">{{ plan.destination }} 旅游计划</h2>
                    <span class="badge bg-{{ plan.status_color }}">{{ plan.status_text }}</span>
                </div>

                <div class="row mb-4">
                    <div class="col-md-6">
                        <h5>基本信息</h5>
                        <table class="table">
                            <tr>
                                <th>出发地：</th>
                                <td>{{ plan.departure }}</td>
                            </tr>
                            <tr>
                                <th>目的地：</th>
                                <td>{{ plan.destination }}</td>
                            </tr>
                            <tr>
                                <th>出发日期：</th>
                                <td>{{ plan.start_date.strftime('%Y-%m-%d') }}</td>
                            </tr>
                            <tr>
                                <th>行程天数：</th>
                                <td>{{ plan.duration }}天</td>
                            </tr>
                            <tr>
                                <th>创建时间：</th>
                                <td>{{ plan.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                            </tr>
                        </table>
                    </div>
                    <div class="col-md-6">
                        <h5>偏好设置</h5>
                        <div class="mb-3">
                            {% set preference_list = plan.preferences.split(',') if plan.preferences else [] %}
                            {% set preference_translations = {
                                'nature': '自然风光',
                                'culture': '文化古迹',
                                'wellness': '康养休闲',
                                'food': '美食体验'
                            } %}
                            {% for pref in preference_list %}
                            <span class="badge bg-info me-2">{{ preference_translations.get(pref, pref) }}</span>
                            {% endfor %}
                        </div>

                        {% if plan.special_needs %}
                        <h5>特殊需求</h5>
                        <p class="card-text">{{ plan.special_needs }}</p>
                        {% endif %}
                    </div>
                </div>

                <h5 class="mb-3">推荐景点</h5>
                {% if plan.spots %}
                <div class="list-group">
                    {% for spot in plan.spots %}
                    <div class="list-group-item">
                        <div class="d-flex w-100 justify-content-between">
                            <h5 class="mb-1">{{ spot.name }}</h5>
                            <small>
                                <i class="bi bi-star-fill text-warning"></i>
                                {{ "%.1f"|format(spot.rating if spot.rating else 5.0) }}
                            </small>
                        </div>
                        <p class="mb-1">{{ spot.description or '暂无描述' }}</p>
                        <small class="text-muted">
                            <i class="bi bi-geo-alt"></i>
                            经度: {{ "%.4f"|format(spot.longitude) }},
                            纬度: {{ "%.4f"|format(spot.latitude) }}
                            {% if spot.visit_duration %}
                            <span class="ms-3">
                                <i class="bi bi-clock"></i>
                                建议游览时间: {{ spot.visit_duration }}分钟
                            </span>
                            {% endif %}
                            {% if spot.health_benefits %}
                            <div class="mt-1">
                                <i class="bi bi-heart"></i>
                                健康效益: {{ spot.health_benefits }}
                            </div>
                            {% endif %}
                        </small>
                    </div>
                    {% endfor %}
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="bi bi-info-circle"></i> 暂无推荐景点，系统将根据您的偏好生成推荐。
                </div>
                {% endif %}
            </div>
        </div>
    </div>

    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title mb-3">操作</h5>
                <div class="d-grid gap-2">
                    <a href="{{ url_for('main.edit_plan', plan_id=plan.id) }}" class="btn btn-primary">
                        <i class="bi bi-pencil"></i> 编辑计划
                    </a>
                    <button type="button" class="btn btn-success" onclick="generateRoute()">
                        <i class="bi bi-map"></i> 生成路线
                    </button>
                    <button type="button" class="btn btn-info" onclick="showMap()">
                        <i class="bi bi-geo"></i> 查看地图
                    </button>
                    <button type="button" class="btn btn-danger" onclick="deletePlan({{ plan.id }})">
                        <i class="bi bi-trash"></i> 删除计划
                    </button>
                </div>
            </div>
        </div>

        <!-- 路线详情面板 -->
        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title mb-3">
                    <i class="bi bi-map"></i> 路线详情
                </h5>
                <div class="route-info">
                    <div class="total-distance">
                        <i class="bi bi-signpost-2"></i>
                        <span>总距离: <span id="total-distance">计算中...</span></span>
                    </div>
                    <div class="total-time">
                        <i class="bi bi-clock"></i>
                        <span>预计用时: <span id="total-time">计算中...</span></span>
                    </div>
                </div>
                <div id="path-panel"></div>
            </div>
        </div>

        <div class="card mt-4">
            <div class="card-body">
                <h5 class="card-title mb-3">天气预报</h5>
                <div class="alert alert-info">
                    <i class="bi bi-cloud-sun"></i>
                    正在获取 {{ plan.destination }} 的天气信息...
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 地图模态框 -->
<div class="modal fade" id="mapModal" tabindex="-1">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">
                    <i class="bi bi-map"></i>
                    从 {{ plan.departure }} 到 {{ plan.destination }} 的路线
                </h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body">
                <div id="map"></div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">关闭</button>
                <button type="button" class="btn btn-primary" onclick="generateRoute()">
                    <i class="bi bi-arrow-repeat"></i> 重新规划
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<!-- 高德地图 JavaScript API -->
<script type="text/javascript">
    window._AMapSecurityConfig = {
        securityJsCode: '{{ config.AMAP_SECURITY_CODE }}'
    }
</script>
<script type="text/javascript" src="https://webapi.amap.com/maps?v=2.0&key={{ config.AMAP_KEY }}&plugin=AMap.Scale,AMap.ToolBar,AMap.Driving,AMap.Geocoder"></script>

<script>
let map = null;
let markers = [];
let driving = null;
let geocoder = null;

function initMap() {
    if (map) {
        return; // 如果地图已经初始化，直接返回
    }

    console.log('Initializing map...');

    try {
        // 创建地图实例
        map = new AMap.Map('map', {
            zoom: 11,
            center: [116.397428, 39.90923], // 默认中心点（北京）
            viewMode: '2D',
            lang: 'zh_cn'
        });

        // 创建地理编码实例
        geocoder = new AMap.Geocoder({
            city: "全国"
        });

        // 创建驾车规划实例
        driving = new AMap.Driving({
            map: map,
            panel: "path-panel",
            policy: AMap.DrivingPolicy.LEAST_TIME,
            showTraffic: true,
            autoFitView: true
        });

        // 添加控件
        map.addControl(new AMap.Scale());
        map.addControl(new AMap.ToolBar({
            position: 'RB'
        }));

        console.log('Map initialization completed');
    } catch (error) {
        console.error('Error initializing map:', error);
        alert('地图加载失败，请刷新页面重试');
    }
}

function addSpotMarker(spot) {
    console.log('Adding marker for spot:', spot);
    const marker = new AMap.Marker({
        position: new AMap.LngLat(spot.longitude, spot.latitude),
        title: spot.name,
        animation: 'AMAP_ANIMATION_DROP'  // 添加点降落动画
    });

    const infoWindow = new AMap.InfoWindow({
        content: `
            <div class="info-window">
                <h5>${spot.name}</h5>
                <p>${spot.description || '暂无描述'}</p>
                <p><strong>评分：</strong>${spot.rating || 5.0}</p>
                ${spot.visit_duration ? `<p><strong>建议游览时间：</strong>${spot.visit_duration}分钟</p>` : ''}
                ${spot.health_benefits ? `<p><strong>健康效益：</strong>${spot.health_benefits}</p>` : ''}
            </div>
        `,
        offset: new AMap.Pixel(0, -30)
    });

    marker.on('click', () => {
        infoWindow.open(map, marker.getPosition());
    });

    marker.setMap(map);
    markers.push(marker);
    return marker;
}

function showMap() {
    console.log('Showing map...');
    var myModal = new bootstrap.Modal(document.getElementById('mapModal'));
    myModal.show();

    // 确保地图容器已经显示
    myModal._element.addEventListener('shown.bs.modal', function () {
        console.log('Modal shown');
        if (!map) {
            initMap();
        }

        // 清除现有标记
        markers.forEach(marker => marker.setMap(null));
        markers = [];

        // 添加景点标记
        const spots = {{ plan.spots|tojson|safe }};
        console.log('Spots data:', spots);

        if (spots && spots.length > 0) {
            spots.forEach(spot => {
                addSpotMarker(spot);
            });

            // 调整地图视野以包含所有标记
            map.setFitView();
        }
    });
}

function generateRoute() {
    if (!map || !driving) {
        showMap();
        return;
    }

    try {
        const spots = {{ plan.spots|tojson|safe }};
        if (!spots || spots.length < 1) {
            alert('需要至少一个景点才能生成路线！');
            return;
        }

        // 清除现有路线
        driving.clear();

        // 使用地理编码服务获取出发地和目的地坐标
        geocoder.getLocation('{{ plan.departure }}', function(status, result) {
            if (status === 'complete' && result.geocodes.length) {
                const departureLocation = result.geocodes[0].location;

                // 获取目的地坐标
                geocoder.getLocation('{{ plan.destination }}', function(status2, result2) {
                    if (status2 === 'complete' && result2.geocodes.length) {
                        const destinationLocation = result2.geocodes[0].location;

                        // 构建途经点（所有景点）
                        const waypoints = spots.map(spot => [spot.longitude, spot.latitude]);

                        // 规划路线
                        driving.search(
                            [departureLocation.lng, departureLocation.lat],  // 起点（出发地）
                            [destinationLocation.lng, destinationLocation.lat],  // 终点（目的地）
                            {
                                waypoints: waypoints  // 途经点（所有景点）
                            },
                            function(status, result) {
                                if (status === 'complete') {
                                    console.log('路线规划成功:', result);

                                    // 更新路线信息
                                    if (result.routes && result.routes[0]) {
                                        const route = result.routes[0];
                                        document.getElementById('total-distance').textContent =
                                            (route.distance / 1000).toFixed(1) + ' 公里';
                                        document.getElementById('total-time').textContent =
                                            Math.ceil(route.time / 60) + ' 分钟';
                                    }

                                    // 调整地图视野以显示所有路线
                                    map.setFitView();
                                } else {
                                    console.error('路线规划失败：', result);
                                    alert('路线规划失败，请稍后重试');
                                }
                            }
                        );
                    } else {
                        console.error('目的地地理编码失败：', result2);
                        alert('无法获取目的地位置，请检查地址是否正确');
                    }
                });
            } else {
                console.error('出发地地理编码失败：', result);
                alert('无法获取出发地位置，请检查地址是否正确');
            }
        });
    } catch (error) {
        console.error('生成路线时发生错误：', error);
        alert('生成路线失败，请稍后重试');
    }
}

function deletePlan(planId) {
    if (confirm('确定要删除这个旅游计划吗？此操作不可恢复。')) {
        fetch(`/plan/${planId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                window.location.href = "{{ url_for('main.my_plans') }}";
            } else {
                alert('删除失败，请稍后重试');
            }
        }).catch(error => {
            console.error('Error:', error);
            alert('删除失败，请稍后重试');
        });
    }
}
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
<style>
.list-group-item {
    transition: all 0.2s;
}
.list-group-item:hover {
    background-color: #f8f9fa;
}
#map {
    height: 400px;
    width: 100%;
    border-radius: 4px;
}
#path-panel {
    max-height: 400px;
    overflow-y: auto;
    padding: 15px;
    background-color: #fff;
    border-radius: 4px;
}
#path-panel .amap-lib-driving {
    border: none;
    border-radius: 4px;
    box-shadow: none;
}
#path-panel .amap-lib-driving-header {
    background-color: #f8f9fa;
    padding: 10px;
    border-radius: 4px;
    margin-bottom: 10px;
}
#path-panel .amap-lib-driving-title {
    color: #333;
    font-weight: bold;
}
#path-panel .amap-lib-driving-content {
    padding: 0 10px;
}
.info-window {
    padding: 15px;
    max-width: 300px;
}
.info-window h5 {
    margin-bottom: 10px;
    color: #333;
}
.info-window p {
    margin-bottom: 8px;
    color: #666;
}
.route-info {
    margin-bottom: 15px;
    padding: 10px;
    background-color: #f8f9fa;
    border-radius: 4px;
}
.route-info .total-distance,
.route-info .total-time {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}
.route-info .total-distance i,
.route-info .total-time i {
    margin-right: 5px;
    color: #0d6efd;
}
</style>
{% endblock %}