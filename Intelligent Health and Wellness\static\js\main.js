// 全局变量
let map = null;
let markers = [];
let routeLayer = null;

// 初始化地图
function initMap() {
    if (!map) {
        map = new AMap.Map('map', {
            zoom: 4,
            center: [104.1954, 35.8617]  // 中国中心位置
        });
        
        // 添加地图控件
        map.addControl(new AMap.Scale());
        map.addControl(new AMap.ToolBar());
        map.addControl(new AMap.MapType());
    }
}

// 清除地图上的所有标记和路线
function clearMap() {
    markers.forEach(marker => map.remove(marker));
    markers = [];
    if (routeLayer) {
        map.remove(routeLayer);
        routeLayer = null;
    }
}

// 添加景点标记
function addSpotMarker(spot) {
    const marker = new AMap.Marker({
        position: [spot.longitude, spot.latitude],
        title: spot.name
    });
    
    const infoWindow = new AMap.InfoWindow({
        content: createSpotPopup(spot),
        offset: new AMap.Pixel(0, -30)
    });
    
    marker.on('click', () => {
        infoWindow.open(map, marker.getPosition());
    });
    
    map.add(marker);
    markers.push(marker);
    return marker;
}

// 创建景点弹出窗口
function createSpotPopup(spot) {
    return `
        <div class="spot-popup">
            <h5>${spot.name}</h5>
            <p>${spot.description}</p>
            <p><strong>健康效益：</strong>${spot.health_benefits}</p>
            <p><strong>建议游览时间：</strong>${spot.visit_duration}分钟</p>
        </div>
    `;
}

// 绘制路线
function drawRoute(route) {
    if (routeLayer) {
        map.remove(routeLayer);
    }
    
    const path = route.map(spot => [spot.longitude, spot.latitude]);
    routeLayer = new AMap.Polyline({
        path: path,
        strokeColor: '#007bff',
        strokeWeight: 3,
        strokeOpacity: 0.7
    });
    
    map.add(routeLayer);
    map.setFitView([routeLayer]);
}

// 更新推荐景点
function updateRecommendedSpots(spots) {
    const container = document.getElementById('recommendedSpots');
    if (!container) return;
    
    container.innerHTML = spots.map(spot => `
        <div class="spot-card mb-3">
            <div class="card">
                <div class="card-body">
                    <h5 class="card-title">${spot.name}</h5>
                    <p class="card-text">${spot.description}</p>
                    <button class="btn btn-sm btn-outline-primary" onclick="addSpotToPlan(${spot.id})">
                        添加到行程
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

// 更新健康建议
function updateHealthTips(preferences) {
    const container = document.getElementById('healthTips');
    if (!container) return;
    
    const tips = [];
    if (preferences.includes('nature')) {
        tips.push('建议选择空气清新的自然景区，有助于身心放松');
    }
    if (preferences.includes('culture')) {
        tips.push('参观人文景点时注意适当休息，避免过度疲劳');
    }
    if (preferences.includes('health')) {
        tips.push('建议选择配备完善康养设施的景区，确保舒适体验');
    }
    
    container.innerHTML = tips.map(tip => `<li>${tip}</li>`).join('');
}

// 表单验证
function validateForm(form) {
    const startDate = new Date(form.start_date.value);
    const endDate = new Date(form.end_date.value);
    
    if (endDate < startDate) {
        alert('结束日期不能早于开始日期！');
        return false;
    }
    
    const preferences = Array.from(form.preferences)
        .filter(checkbox => checkbox.checked)
        .map(checkbox => checkbox.value);
    
    if (preferences.length === 0) {
        alert('请至少选择一个偏好！');
        return false;
    }
    
    return true;
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    initMap();
    
    // 表单提交处理
    const planForm = document.getElementById('planForm');
    if (planForm) {
        planForm.addEventListener('submit', function(e) {
            e.preventDefault();
            if (validateForm(this)) {
                // TODO: 实现表单提交逻辑
                console.log('表单验证通过，准备提交...');
            }
        });
    }
    
    // 偏好选择处理
    const preferenceCheckboxes = document.querySelectorAll('input[name="preferences"]');
    preferenceCheckboxes.forEach(checkbox => {
        checkbox.addEventListener('change', function() {
            const preferences = Array.from(preferenceCheckboxes)
                .filter(cb => cb.checked)
                .map(cb => cb.value);
            updateHealthTips(preferences);
        });
    });
}); 