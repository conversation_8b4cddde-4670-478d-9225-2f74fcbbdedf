{% extends "base.html" %}

{% block title %}我的旅游计划 - 智能康养旅游线路规划系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title mb-4">我的旅游计划</h2>
                {% if plans %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead>
                            <tr>
                                <th>目的地</th>
                                <th>出发日期</th>
                                <th>行程天数</th>
                                <th>创建时间</th>
                                <th>状态</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for plan in plans %}
                            <tr>
                                <td>{{ plan.destination }}</td>
                                <td>{{ plan.start_date.strftime('%Y-%m-%d') }}</td>
                                <td>{{ plan.duration }}天</td>
                                <td>{{ plan.created_at.strftime('%Y-%m-%d %H:%M') }}</td>
                                <td>
                                    <span class="badge bg-{{ plan.status_color }}">{{ plan.status_text }}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="{{ url_for('main.view_plan', plan_id=plan.id) }}" class="btn btn-sm btn-outline-primary">
                                            <i class="bi bi-eye"></i> 查看
                                        </a>
                                        <a href="{{ url_for('main.edit_plan', plan_id=plan.id) }}" class="btn btn-sm btn-outline-secondary">
                                            <i class="bi bi-pencil"></i> 编辑
                                        </a>
                                        <button type="button" class="btn btn-sm btn-outline-danger" 
                                                onclick="deletePlan({{ plan.id }})">
                                            <i class="bi bi-trash"></i> 删除
                                        </button>
                                    </div>
                                </td>
                            </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="text-center py-5">
                    <i class="bi bi-calendar3 display-1 text-muted mb-3"></i>
                    <h3 class="text-muted">暂无旅游计划</h3>
                    <p class="text-muted mb-4">开始创建您的第一个旅游计划吧！</p>
                    <a href="{{ url_for('main.create_plan') }}" class="btn btn-primary">
                        <i class="bi bi-plus-lg"></i> 创建新计划
                    </a>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
function deletePlan(planId) {
    if (confirm('确定要删除这个旅游计划吗？此操作不可恢复。')) {
        fetch(`/plan/${planId}`, {
            method: 'DELETE',
            headers: {
                'Content-Type': 'application/json'
            }
        }).then(response => {
            if (response.ok) {
                window.location.reload();
            } else {
                alert('删除失败，请稍后重试');
            }
        }).catch(error => {
            console.error('Error:', error);
            alert('删除失败，请稍后重试');
        });
    }
}
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
<style>
.table td {
    vertical-align: middle;
}
</style>
{% endblock %} 