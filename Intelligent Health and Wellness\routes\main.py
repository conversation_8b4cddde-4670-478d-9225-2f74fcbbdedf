from flask import Blueprint, render_template, request, redirect, url_for, flash, make_response, jsonify, current_app, session
from models.travel_plan import TravelPlan
from models.scenic_spot import ScenicSpot
from extensions import db
from datetime import datetime
from routes.auth import login_required

main_bp = Blueprint('main', __name__)

@main_bp.route('/index')
@login_required
def index():
    response = make_response(render_template('index.html'))
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@main_bp.route('/create_plan', methods=['GET', 'POST'])
@login_required
def create_plan():
    if request.method == 'POST':
        try:
            # 获取表单数据
            departure = request.form.get('departure', '西安')  # 添加出发地，默认为西安
            destination = request.form.get('destination')
            start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d')
            duration = int(request.form.get('duration'))
            preferences = request.form.getlist('preferences')
            special_needs = request.form.get('special_needs')

            # 创建新的旅游计划
            new_plan = TravelPlan(
                departure=departure,  # 添加出发地
                destination=destination,
                start_date=start_date,
                duration=duration,
                preferences=','.join(preferences) if preferences else '',
                special_needs=special_needs
            )

            # 根据目的地添加默认景点
            spots = ScenicSpot.query.limit(5).all()  # 临时添加一些景点用于测试
            new_plan.spots.extend(spots)

            # 保存到数据库
            db.session.add(new_plan)
            db.session.commit()

            flash('旅游计划创建成功！', 'success')
            return redirect(url_for('main.view_plan', plan_id=new_plan.id))
        except Exception as e:
            db.session.rollback()
            flash(f'创建计划失败：{str(e)}', 'danger')
            return redirect(url_for('main.create_plan'))

    response = make_response(render_template('create_plan.html'))
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@main_bp.route('/my_plans')
@login_required
def my_plans():
    plans = TravelPlan.query.order_by(TravelPlan.created_at.desc()).all()
    response = make_response(render_template('my_plans.html', plans=plans))
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@main_bp.route('/plan/<int:plan_id>')
@login_required
def view_plan(plan_id):
    plan = TravelPlan.query.get_or_404(plan_id)
    # 将景点对象转换为字典
    plan_data = {
        'id': plan.id,
        'departure': plan.departure,  # 添加出发地
        'destination': plan.destination,
        'start_date': plan.start_date,
        'duration': plan.duration,
        'preferences': plan.preferences,
        'special_needs': plan.special_needs,
        'status': plan.status,
        'status_text': plan.status_text,
        'status_color': plan.status_color,
        'created_at': plan.created_at,
        'spots': [spot.to_dict() for spot in plan.spots]
    }
    # 添加调试信息
    print("Plan spots:", plan_data['spots'])
    response = make_response(render_template('view_plan.html', plan=plan_data))
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@main_bp.route('/plan/<int:plan_id>/edit', methods=['GET', 'POST'])
def edit_plan(plan_id):
    plan = TravelPlan.query.get_or_404(plan_id)

    if request.method == 'POST':
        try:
            # 更新计划数据
            plan.destination = request.form.get('destination')
            plan.start_date = datetime.strptime(request.form.get('start_date'), '%Y-%m-%d')
            plan.duration = int(request.form.get('duration'))
            plan.preferences = ','.join(request.form.getlist('preferences'))
            plan.special_needs = request.form.get('special_needs')
            plan.status = request.form.get('status')

            # 保存到数据库
            db.session.commit()

            flash('旅游计划更新成功！', 'success')
            return redirect(url_for('main.view_plan', plan_id=plan.id))
        except Exception as e:
            db.session.rollback()
            flash(f'更新计划失败：{str(e)}', 'danger')
            return redirect(url_for('main.edit_plan', plan_id=plan.id))

    response = make_response(render_template('edit_plan.html', plan=plan))
    response.headers['Content-Type'] = 'text/html; charset=utf-8'
    return response

@main_bp.route('/plan/<int:plan_id>', methods=['DELETE'])
def delete_plan(plan_id):
    plan = TravelPlan.query.get_or_404(plan_id)
    try:
        db.session.delete(plan)
        db.session.commit()
        return jsonify({'message': '计划删除成功'}), 200
    except Exception as e:
        db.session.rollback()
        return jsonify({'error': str(e)}), 500

@main_bp.route('/api/plan', methods=['POST'])
def create_travel_plan():
    data = request.get_json()
    # TODO: 实现路线规划算法
    return jsonify({'status': 'success', 'message': '路线规划成功'})

@main_bp.route('/api/spots')
def get_scenic_spots():
    spots = ScenicSpot.query.all()
    return jsonify([spot.to_dict() for spot in spots])