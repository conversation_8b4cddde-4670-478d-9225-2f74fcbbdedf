{% extends "base.html" %}

{% block title %}编辑旅游计划 - 智能康养旅游线路规划系统{% endblock %}

{% block content %}
<div class="row">
    <div class="col-md-8">
        <div class="card">
            <div class="card-body">
                <h2 class="card-title mb-4">编辑旅游计划</h2>
                <form method="POST" action="{{ url_for('main.edit_plan', plan_id=plan.id) }}" id="editPlanForm">
                    <div class="mb-3">
                        <label for="destination" class="form-label">目的地</label>
                        <input type="text" class="form-control" id="destination" name="destination" 
                               value="{{ plan.destination }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="start_date" class="form-label">出发日期</label>
                        <input type="date" class="form-control" id="start_date" name="start_date" 
                               value="{{ plan.start_date.strftime('%Y-%m-%d') }}" required>
                    </div>
                    
                    <div class="mb-3">
                        <label for="duration" class="form-label">行程天数</label>
                        <select class="form-select" id="duration" name="duration" required>
                            <option value="">请选择</option>
                            {% for days in [3, 5, 7, 10] %}
                            <option value="{{ days }}" {% if plan.duration == days %}selected{% endif %}>{{ days }}天</option>
                            {% endfor %}
                        </select>
                    </div>
                    
                    <div class="mb-3">
                        <label class="form-label">旅游偏好</label>
                        {% set preferences = plan.preferences.split(',') if plan.preferences else [] %}
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="nature" name="preferences" 
                                   value="nature" {% if 'nature' in preferences %}checked{% endif %}>
                            <label class="form-check-label" for="nature">自然风光</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="culture" name="preferences" 
                                   value="culture" {% if 'culture' in preferences %}checked{% endif %}>
                            <label class="form-check-label" for="culture">文化古迹</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="wellness" name="preferences" 
                                   value="wellness" {% if 'wellness' in preferences %}checked{% endif %}>
                            <label class="form-check-label" for="wellness">康养设施</label>
                        </div>
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="food" name="preferences" 
                                   value="food" {% if 'food' in preferences %}checked{% endif %}>
                            <label class="form-check-label" for="food">美食体验</label>
                        </div>
                    </div>
                    
                    <div class="mb-3">
                        <label for="special_needs" class="form-label">特殊需求</label>
                        <textarea class="form-control" id="special_needs" name="special_needs" 
                                  rows="3" placeholder="请输入您的特殊需求或健康考虑...">{{ plan.special_needs }}</textarea>
                    </div>
                    
                    <div class="mb-3">
                        <label for="status" class="form-label">计划状态</label>
                        <select class="form-select" id="status" name="status">
                            <option value="draft" {% if plan.status == 'draft' %}selected{% endif %}>草稿</option>
                            <option value="planning" {% if plan.status == 'planning' %}selected{% endif %}>规划中</option>
                            <option value="confirmed" {% if plan.status == 'confirmed' %}selected{% endif %}>已确认</option>
                            <option value="completed" {% if plan.status == 'completed' %}selected{% endif %}>已完成</option>
                        </select>
                    </div>
                    
                    <div class="d-flex justify-content-between">
                        <button type="submit" class="btn btn-primary">保存修改</button>
                        <a href="{{ url_for('main.view_plan', plan_id=plan.id) }}" class="btn btn-secondary">取消</a>
                    </div>
                </form>
            </div>
        </div>
    </div>
    
    <div class="col-md-4">
        <div class="card">
            <div class="card-body">
                <h5 class="card-title mb-3">编辑说明</h5>
                <div class="alert alert-info">
                    <h6 class="alert-heading">温馨提示：</h6>
                    <ul class="mb-0">
                        <li>修改目的地将重新规划路线</li>
                        <li>更改偏好会影响景点推荐</li>
                        <li>状态变更会影响计划显示</li>
                    </ul>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 日期验证：设置最小日期为今天
    var today = new Date();
    var dd = String(today.getDate()).padStart(2, '0');
    var mm = String(today.getMonth() + 1).padStart(2, '0');
    var yyyy = today.getFullYear();
    today = yyyy + '-' + mm + '-' + dd;
    
    var startDateInput = document.getElementById('start_date');
    startDateInput.min = today;
    
    // 表单验证
    document.getElementById('editPlanForm').addEventListener('submit', function(e) {
        var destination = document.getElementById('destination').value.trim();
        var startDate = document.getElementById('start_date').value;
        var duration = document.getElementById('duration').value;
        var preferences = document.querySelectorAll('input[name="preferences"]:checked');
        
        if (!destination) {
            e.preventDefault();
            alert('请输入目的地');
            return false;
        }
        
        if (!startDate) {
            e.preventDefault();
            alert('请选择出发日期');
            return false;
        }
        
        if (!duration) {
            e.preventDefault();
            alert('请选择行程天数');
            return false;
        }
        
        if (preferences.length === 0) {
            e.preventDefault();
            alert('请至少选择一个旅游偏好');
            return false;
        }
        
        return true;
    });
});
</script>
{% endblock %}

{% block extra_css %}
<link href="https://cdn.bootcdn.net/ajax/libs/bootstrap-icons/1.7.2/font/bootstrap-icons.min.css" rel="stylesheet">
<style>
.form-check {
    margin-bottom: 0.5rem;
}
</style>
{% endblock %} 