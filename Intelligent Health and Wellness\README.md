# 基于大模型技术的智能康养旅游线路规划系统

## 项目简介
本项目是一个基于Python开发的智能康养旅游线路规划系统，旨在帮助用户快速获得符合个人需求的康养旅游线路。

## 主要功能
1. 旅游需求分析
2. 智能路线规划
3. 系统展示界面
4. 数据可视化
5. 结果评估

## 技术栈
- 后端：Python + Flask
- 数据库：MySQL
- 前端：HTML + CSS + JavaScript
- 地图API：OpenStreetMap

## 安装说明
1. 克隆项目到本地
2. 安装依赖：
```bash
pip install -r requirements.txt
```
3. 配置数据库：
   - 创建MySQL数据库
   - 修改 config.py 中的数据库配置
4. 运行项目：
```bash
python app.py
```

## 项目结构
```
├── app.py              # 主应用入口
├── config.py           # 配置文件
├── requirements.txt    # 项目依赖
├── static/            # 静态文件
├── templates/         # HTML模板
├── models/           # 数据模型
├── utils/            # 工具函数
└── data/             # 数据文件
```

## 作者信息
- 姓名：赵玉
- 学校：商洛学院
- 邮箱：<EMAIL> 