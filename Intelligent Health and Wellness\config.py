import os
from dotenv import load_dotenv

load_dotenv()

class Config:
    # 数据库配置
    SQLALCHEMY_DATABASE_URI = 'mysql+pymysql://root:root@localhost/travel_planner'
    SQLALCHEMY_TRACK_MODIFICATIONS = False
    
    # 安全配置
    SECRET_KEY = os.getenv('SECRET_KEY', 'your-secret-key-here')
    
    # 高德地图API配置
    AMAP_KEY = '0652d50364ecd87d6363bbdf9627739c'  # Web端 JS API密钥
    AMAP_SECURITY_CODE = '66f5762ebd6602a4d6aa44829fa31dfb'  # 安全密钥
    
    # 应用配置
    DEBUG = True
    HOST = '0.0.0.0'
    PORT = 5001