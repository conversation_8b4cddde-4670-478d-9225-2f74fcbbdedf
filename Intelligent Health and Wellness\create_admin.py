from app import app, db
from models.user import User

def create_admin_user():
    """创建一个管理员用户"""
    with app.app_context():
        # 检查用户表是否存在
        try:
            # 检查是否已存在admin用户
            admin = User.query.filter_by(username='admin').first()
            if admin:
                print('管理员用户已存在')
                return
            
            # 创建新的管理员用户
            admin = User(username='admin')
            admin.set_password('admin123')
            
            db.session.add(admin)
            db.session.commit()
            print('管理员用户创建成功！用户名: admin, 密码: admin123')
        except Exception as e:
            print(f'创建管理员用户时出错: {str(e)}')
            db.session.rollback()

if __name__ == '__main__':
    create_admin_user()
