from app import app, db
from models.scenic_spot import ScenicSpot

def add_spots():
    spots = [
        {
            'name': '故宫',
            'description': '中国明清两代的皇家宫殿，世界上现存规模最大、保存最完整的木质结构古建筑之一',
            'latitude': 39.9163,
            'longitude': 116.3972,
            'health_benefits': '漫步古建筑群可以放松身心，感受历史文化的熏陶',
            'visit_duration': 240,
            'rating': 4.9
        },
        {
            'name': '天安门广场',
            'description': '世界上最大的城市广场，是首都北京的象征性建筑',
            'latitude': 39.9054,
            'longitude': 116.3976,
            'health_benefits': '广场漫步有助于放松心情，提供舒适的散步环境',
            'visit_duration': 120,
            'rating': 4.8
        },
        {
            'name': '颐和园',
            'description': '中国现存最大的皇家园林，被誉为皇家园林博物馆',
            'latitude': 39.9999,
            'longitude': 116.2751,
            'health_benefits': '园林漫步可以陶冶情操，湖边散步有助于身心放松',
            'visit_duration': 180,
            'rating': 4.8
        },
        {
            'name': '八达岭长城',
            'description': '万里长城最具代表性的段落之一，是明长城的精华所在',
            'latitude': 40.3592,
            'longitude': 116.0202,
            'health_benefits': '登长城是很好的有氧运动，可以锻炼心肺功能',
            'visit_duration': 180,
            'rating': 4.7
        },
        {
            'name': '天坛',
            'description': '中国最大的古代帝王祭天建筑群',
            'latitude': 39.8822,
            'longitude': 116.4107,
            'health_benefits': '园区散步可以放松身心，提供宁静的环境',
            'visit_duration': 150,
            'rating': 4.8
        },
        {
            'name': '南锣鼓巷',
            'description': '北京最古老的胡同之一，展现老北京的传统生活风貌',
            'latitude': 39.9369,
            'longitude': 116.4031,
            'health_benefits': '漫步胡同感受老北京文化，品尝特色小吃',
            'visit_duration': 120,
            'rating': 4.6
        },
        {
            'name': '奥林匹克公园',
            'description': '2008年北京奥运会的主会场所在地',
            'latitude': 40.0025,
            'longitude': 116.3975,
            'health_benefits': '提供良好的运动和休闲环境，适合健步走和跑步',
            'visit_duration': 150,
            'rating': 4.7
        },
        {
            'name': '北海公园',
            'description': '中国现存最古老的皇家园林之一',
            'latitude': 39.9263,
            'longitude': 116.3906,
            'health_benefits': '园林漫步可以陶冶情操，湖边散步有助于身心放松',
            'visit_duration': 120,
            'rating': 4.7
        },
        {
            'name': '恭王府',
            'description': '清代规模最大的王府，被誉为皇城根下第一府',
            'latitude': 39.9405,
            'longitude': 116.3849,
            'health_benefits': '参观古建筑可以增长见识，感受历史文化',
            'visit_duration': 90,
            'rating': 4.6
        },
        {
            'name': '雍和宫',
            'description': '清朝著名的皇家寺院，是中国藏传佛教最重要的寺院之一',
            'latitude': 39.9477,
            'longitude': 116.4173,
            'health_benefits': '寺院环境安静祥和，有助于心灵放松',
            'visit_duration': 90,
            'rating': 4.7
        },
        {
            'name': '什刹海',
            'description': '北京最著名的历史文化景区之一',
            'latitude': 39.9442,
            'longitude': 116.3738,
            'health_benefits': '湖区漫步可以放松身心，欣赏美景',
            'visit_duration': 150,
            'rating': 4.6
        },
        {
            'name': '鸟巢',
            'description': '2008年北京奥运会主会场，中国新的地标性建筑',
            'latitude': 40.0024,
            'longitude': 116.3967,
            'health_benefits': '现代建筑参观，体验奥运氛围',
            'visit_duration': 60,
            'rating': 4.8
        },
        {
            'name': '水立方',
            'description': '2008年北京奥运会游泳馆，独特的建筑设计',
            'latitude': 40.0032,
            'longitude': 116.3907,
            'health_benefits': '现代建筑参观，提供游泳运动场所',
            'visit_duration': 60,
            'rating': 4.7
        },
        {
            'name': '香山公园',
            'description': '北京著名的赏红叶胜地，拥有丰富的自然景观',
            'latitude': 40.0095,
            'longitude': 116.1947,
            'health_benefits': '登山健步走，呼吸新鲜空气，欣赏自然美景',
            'visit_duration': 180,
            'rating': 4.7
        },
        {
            'name': '圆明园',
            'description': '清代著名的皇家园林，被誉为万园之园',
            'latitude': 40.0079,
            'longitude': 116.2990,
            'health_benefits': '遗址公园漫步，了解历史文化',
            'visit_duration': 150,
            'rating': 4.6
        }
    ]

    with app.app_context():
        for spot_data in spots:
            spot = ScenicSpot(**spot_data)
            db.session.add(spot)
        db.session.commit()
        print("Successfully added all spots!")

if __name__ == '__main__':
    add_spots() 