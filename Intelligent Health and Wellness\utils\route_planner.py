import numpy as np
from geopy.distance import geodesic
from geopy.geocoders import Nominatim
from typing import List, Dict, Tuple
import heapq

class RoutePlanner:
    def __init__(self):
        self.geocoder = Nominatim(user_agent="travel_planner")
    
    def calculate_distance(self, point1: <PERSON><PERSON>[float, float], point2: <PERSON><PERSON>[float, float]) -> float:
        """计算两点之间的距离（公里）"""
        return geodesic(point1, point2).kilometers
    
    def find_nearest_neighbor(self, current: Tuple[float, float], unvisited: List[Dict]) -> Tuple[Dict, float]:
        """找到最近的未访问景点"""
        min_distance = float('inf')
        nearest = None
        
        for spot in unvisited:
            distance = self.calculate_distance(
                current,
                (spot['latitude'], spot['longitude'])
            )
            if distance < min_distance:
                min_distance = distance
                nearest = spot
                
        return nearest, min_distance
    
    def plan_route(self, spots: List[Dict], start_point: Tuple[float, float], 
                  max_daily_distance: float = 100) -> List[Dict]:
        """使用贪心算法规划路线"""
        if not spots:
            return []
            
        route = []
        unvisited = spots.copy()
        current = start_point
        daily_distance = 0
        day = 1
        
        while unvisited:
            # 找到最近的未访问景点
            nearest, distance = self.find_nearest_neighbor(current, unvisited)
            
            # 如果距离超过每日限制，开始新的一天
            if daily_distance + distance > max_daily_distance:
                day += 1
                daily_distance = 0
                current = start_point  # 返回起点
                continue
            
            # 添加景点到路线
            nearest['visit_day'] = day
            route.append(nearest)
            unvisited.remove(nearest)
            
            # 更新当前位置和距离
            current = (nearest['latitude'], nearest['longitude'])
            daily_distance += distance
        
        return route
    
    def optimize_route(self, route: List[Dict], start_point: Tuple[float, float]) -> List[Dict]:
        """使用2-opt算法优化路线"""
        best_route = route.copy()
        best_distance = self.calculate_total_distance(route, start_point)
        improved = True
        
        while improved:
            improved = False
            for i in range(1, len(route) - 2):
                for j in range(i + 1, len(route)):
                    if j - i == 1:
                        continue
                        
                    new_route = route.copy()
                    new_route[i:j] = route[j-1:i-1:-1]
                    new_distance = self.calculate_total_distance(new_route, start_point)
                    
                    if new_distance < best_distance:
                        best_route = new_route
                        best_distance = new_distance
                        improved = True
            
            route = best_route
            
        return best_route
    
    def calculate_total_distance(self, route: List[Dict], start_point: Tuple[float, float]) -> float:
        """计算路线总距离"""
        if not route:
            return 0
            
        total_distance = self.calculate_distance(start_point, (route[0]['latitude'], route[0]['longitude']))
        
        for i in range(len(route) - 1):
            total_distance += self.calculate_distance(
                (route[i]['latitude'], route[i]['longitude']),
                (route[i+1]['latitude'], route[i+1]['longitude'])
            )
            
        return total_distance 