from extensions import db
from datetime import datetime

class TravelPlan(db.Model):
    __tablename__ = 'travel_plans'
    
    id = db.<PERSON>umn(db.Integer, primary_key=True)
    departure = db.Column(db.String(100), nullable=False, default='西安')
    destination = db.Column(db.String(100), nullable=False)
    start_date = db.Column(db.Date, nullable=False)
    duration = db.Column(db.Integer, nullable=False)
    preferences = db.Column(db.String(200))
    special_needs = db.Column(db.Text)
    created_at = db.Column(db.DateTime, default=datetime.utcnow)
    status = db.Column(db.String(20), default='draft')
    
    # 关联景点
    spots = db.relationship('ScenicSpot', secondary='plan_spots',
                          backref=db.backref('plans', lazy='dynamic'))
    
    @property
    def status_text(self):
        status_map = {
            'draft': '草稿',
            'planning': '规划中',
            'confirmed': '已确认',
            'completed': '已完成'
        }
        return status_map.get(self.status, '未知')

    @property
    def status_color(self):
        color_map = {
            'draft': 'secondary',
            'planning': 'info',
            'confirmed': 'success',
            'completed': 'primary'
        }
        return color_map.get(self.status, 'secondary')

    def to_dict(self):
        return {
            'id': self.id,
            'departure': self.departure,
            'destination': self.destination,
            'start_date': self.start_date.strftime('%Y-%m-%d'),
            'duration': self.duration,
            'preferences': self.preferences,
            'special_needs': self.special_needs,
            'status': self.status,
            'status_text': self.status_text,
            'status_color': self.status_color,
            'spots': [spot.to_dict() for spot in self.spots]
        }

# 旅行计划和景点的关联表
plan_spots = db.Table('plan_spots',
    db.Column('plan_id', db.Integer, db.ForeignKey('travel_plans.id')),
    db.Column('spot_id', db.Integer, db.ForeignKey('scenic_spots.id')),
    db.Column('visit_order', db.Integer),  # 访问顺序
    db.Column('visit_date', db.Date)  # 计划访问日期
) 