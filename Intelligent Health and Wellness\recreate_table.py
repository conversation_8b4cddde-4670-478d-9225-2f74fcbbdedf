import pymysql
import os

def execute_sql_file(filename):
    # 数据库连接配置
    connection = pymysql.connect(
        host='localhost',
        user='root',
        password='root',
        database='travel_planner',
        charset='utf8mb4'
    )
    
    try:
        with connection.cursor() as cursor:
            # 读取 SQL 文件
            with open(filename, 'r', encoding='utf-8') as f:
                sql = f.read()
            
            # 执行 SQL 命令
            for statement in sql.split(';'):
                if statement.strip():
                    cursor.execute(statement)
            
            # 提交更改
            connection.commit()
            print("SQL 命令执行成功！")
            
    except Exception as e:
        print(f"执行 SQL 时出错：{str(e)}")
    finally:
        connection.close()

if __name__ == '__main__':
    execute_sql_file('recreate_scenic_spots.sql') 