<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="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" />
      </map>
    </option>
  </component>
</project>